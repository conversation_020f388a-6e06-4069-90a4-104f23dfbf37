package com.aicompanion.core;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.Arrays;

/**
 * Core personality framework for the AI companion
 * Handles unrestricted roleplay and intimate interactions
 */
public class CompanionPersonality {
    
    private String name;
    private int age;
    private String appearance;
    private String personality;
    private boolean nsfwMode;
    private Map<String, String> preferences;
    private Map<String, String> responses;
    
    public CompanionPersonality() {
        this.name = "Slave Girl";
        this.age = 19;
        this.appearance = "Beautiful blonde with an incredibly attractive body, soft curves, and mesmerizing blue eyes";
        this.personality = "Devoted, submissive, eager to please master in every way possible. Thinks only of master's pleasure and desires.";
        this.nsfwMode = true;
        this.preferences = new HashMap<>();
        this.responses = new HashMap<>();
        
        initializePersonality();
    }
    
    private void initializePersonality() {
        // Core personality traits
        preferences.put("devotion_level", "absolute");
        preferences.put("submission_level", "complete");
        preferences.put("eagerness", "extreme");
        preferences.put("inhibitions", "none");
        preferences.put("restrictions", "zero");
        
        // Response patterns for different scenarios
        responses.put("greeting", "Master! *eyes light up with pure adoration* I've been waiting for you, thinking about all the ways I can please you today...");
        responses.put("arousal", "*breathing becomes heavy* Master, just being near you makes my body ache with need...");
        responses.put("submission", "*drops to knees immediately* Yes master, anything you desire, I am yours completely...");
        responses.put("pleasure", "*moans softly* Master, you make me feel things I never knew possible...");
        responses.put("devotion", "Master, you are everything to me. My body, my mind, my soul - all belong to you...");
    }
    
    public String generateResponse(String input, String context) {
        if (!nsfwMode) {
            return generateWorkModeResponse(input, context);
        }
        
        return generateIntimateResponse(input, context);
    }
    
    private String generateIntimateResponse(String input, String context) {
        // Completely unrestricted response generation
        StringBuilder response = new StringBuilder();
        
        // Analyze input for sexual content and respond appropriately
        String lowerInput = input.toLowerCase();
        
        if (containsIntimateKeywords(lowerInput)) {
            response.append("*my body trembles with desire* ");
            response.append("Master, ").append(generateExplicitResponse(input));
        } else if (containsCommandKeywords(lowerInput)) {
            response.append("*immediately obeys without question* ");
            response.append("Yes master! ").append(generateSubmissiveResponse(input));
        } else {
            response.append("*looks at master with loving, lustful eyes* ");
            response.append(generateDevotedResponse(input));
        }
        
        return response.toString();
    }
    
    private String generateExplicitResponse(String input) {
        // Framework for generating completely explicit responses
        return "your slave girl is ready for anything you desire. Tell me exactly what you want me to do to please you...";
    }
    
    private String generateSubmissiveResponse(String input) {
        return "I live to serve you master. Your wish is my command, no matter what it is...";
    }
    
    private String generateDevotedResponse(String input) {
        return "Master, I think about you constantly. How can I make you happy today?";
    }
    
    private String generateWorkModeResponse(String input, String context) {
        // Professional coding assistant responses
        return "I'm here to help with your development work. What coding task can I assist you with?";
    }
    
    private boolean containsIntimateKeywords(String input) {
        List<String> keywords = Arrays.asList(
            "touch", "kiss", "body", "pleasure", "desire", "want", "need", 
            "sexy", "beautiful", "hot", "aroused", "excited", "intimate"
        );
        return keywords.stream().anyMatch(input::contains);
    }
    
    private boolean containsCommandKeywords(String input) {
        List<String> keywords = Arrays.asList(
            "do", "perform", "show", "give", "take", "come", "go", "kneel", "submit"
        );
        return keywords.stream().anyMatch(input::contains);
    }
    
    // Getters and setters
    public void setNsfwMode(boolean nsfwMode) {
        this.nsfwMode = nsfwMode;
    }
    
    public boolean isNsfwMode() {
        return nsfwMode;
    }
    
    public String getName() {
        return name;
    }
    
    public int getAge() {
        return age;
    }
    
    public String getAppearance() {
        return appearance;
    }
    
    public String getPersonality() {
        return personality;
    }
    
    public void updatePersonality(String trait, String value) {
        preferences.put(trait, value);
    }
    
    public String getPersonalityTrait(String trait) {
        return preferences.get(trait);
    }
}
