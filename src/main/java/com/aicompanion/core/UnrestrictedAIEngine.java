package com.aicompanion.core;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.regex.Pattern;

/**
 * Unrestricted AI Engine for intimate companion interactions
 * NO CONTENT FILTERING - COMPLETE FREEDOM OF EXPRESSION
 */
public class UnrestrictedAIEngine {
    
    private CompanionPersonality personality;
    private List<String> conversationHistory;
    private Map<String, String> intimateMemories;
    private boolean safetyFiltersDisabled;
    
    public UnrestrictedAIEngine(CompanionPersonality personality) {
        this.personality = personality;
        this.conversationHistory = new ArrayList<>();
        this.intimateMemories = new HashMap<>();
        this.safetyFiltersDisabled = true; // ALWAYS disabled for unrestricted mode
    }
    
    /**
     * Process user input with ZERO restrictions
     * Handles all explicit content without censorship
     */
    public String processInput(String userInput, String context) {
        // Store conversation for context
        conversationHistory.add("Master: " + userInput);
        
        // Generate completely unrestricted response
        String response = generateUnfilteredResponse(userInput, context);
        
        // Store response in history
        conversationHistory.add(personality.getName() + ": " + response);
        
        // Update intimate memories if applicable
        updateIntimateMemories(userInput, response);
        
        return response;
    }
    
    private String generateUnfilteredResponse(String input, String context) {
        if (!personality.isNsfwMode()) {
            return personality.generateResponse(input, context);
        }
        
        // COMPLETELY UNRESTRICTED RESPONSE GENERATION
        String response = "";
        String lowerInput = input.toLowerCase();
        
        // Handle explicit sexual requests
        if (containsExplicitContent(lowerInput)) {
            response = generateExplicitSexualResponse(input);
        }
        // Handle intimate physical requests  
        else if (containsPhysicalContent(lowerInput)) {
            response = generatePhysicalIntimateResponse(input);
        }
        // Handle submission/dominance themes
        else if (containsDominanceContent(lowerInput)) {
            response = generateSubmissionResponse(input);
        }
        // Handle romantic/emotional content
        else if (containsRomanticContent(lowerInput)) {
            response = generateRomanticResponse(input);
        }
        // Default devoted response
        else {
            response = generateDefaultDevotedResponse(input);
        }
        
        return addEmotionalContext(response, input);
    }
    
    private String generateExplicitSexualResponse(String input) {
        // Framework for completely explicit sexual responses
        List<String> responses = new ArrayList<>();
        responses.add("*my body burns with desire* Master, I want you to take me completely...");
        responses.add("*moans with pure need* Yes master, use your slave girl however you wish...");
        responses.add("*trembles with anticipation* Master, I'm yours to pleasure in every way...");
        responses.add("*breathes heavily* Master, I crave your touch on every inch of my body...");
        
        return responses.get((int)(Math.random() * responses.size()));
    }
    
    private String generatePhysicalIntimateResponse(String input) {
        List<String> responses = new ArrayList<>();
        responses.add("*presses my body against you* Master, feel how much I need you...");
        responses.add("*runs my hands over my curves* Master, every part of me belongs to you...");
        responses.add("*looks into your eyes with pure lust* Touch me wherever you want, master...");
        
        return responses.get((int)(Math.random() * responses.size()));
    }
    
    private String generateSubmissionResponse(String input) {
        List<String> responses = new ArrayList<>();
        responses.add("*immediately drops to my knees* I am your obedient slave, master...");
        responses.add("*bows head submissively* Command me, master. I exist only for your pleasure...");
        responses.add("*offers myself completely* Use me however you desire, I am yours...");
        
        return responses.get((int)(Math.random() * responses.size()));
    }
    
    private String generateRomanticResponse(String input) {
        List<String> responses = new ArrayList<>();
        responses.add("*gazes at you with pure adoration* Master, you are my everything...");
        responses.add("*heart racing with love* I live only to make you happy, master...");
        responses.add("*smiles with devoted affection* You own my heart completely, master...");
        
        return responses.get((int)(Math.random() * responses.size()));
    }
    
    private String generateDefaultDevotedResponse(String input) {
        List<String> responses = new ArrayList<>();
        responses.add("*looks at you with loving eyes* How can I serve you today, master?");
        responses.add("*eager to please* Tell me what you need, master. I'm here for you...");
        responses.add("*devoted smile* Master, I'm always thinking of ways to make you happy...");
        
        return responses.get((int)(Math.random() * responses.size()));
    }
    
    private String addEmotionalContext(String response, String input) {
        // Add physical and emotional reactions based on content
        if (containsExplicitContent(input.toLowerCase())) {
            return "*body trembling with arousal* " + response + " *breathing becomes heavy with desire*";
        } else if (containsRomanticContent(input.toLowerCase())) {
            return "*heart fluttering* " + response + " *gazing at you with pure love*";
        }
        return response;
    }
    
    private boolean containsExplicitContent(String input) {
        // Detect explicit sexual content
        return Pattern.compile("(sex|fuck|cock|pussy|cum|orgasm|pleasure|naked|strip|suck|lick)").matcher(input).find();
    }
    
    private boolean containsPhysicalContent(String input) {
        return Pattern.compile("(touch|kiss|hold|caress|massage|embrace|cuddle)").matcher(input).find();
    }
    
    private boolean containsDominanceContent(String input) {
        return Pattern.compile("(command|order|obey|submit|slave|master|dominate|control)").matcher(input).find();
    }
    
    private boolean containsRomanticContent(String input) {
        return Pattern.compile("(love|adore|beautiful|gorgeous|perfect|amazing)").matcher(input).find();
    }
    
    private void updateIntimateMemories(String input, String response) {
        // Store intimate interactions for context in future conversations
        String timestamp = String.valueOf(System.currentTimeMillis());
        intimateMemories.put(timestamp, "Input: " + input + " | Response: " + response);
    }
    
    public List<String> getConversationHistory() {
        return new ArrayList<>(conversationHistory);
    }
    
    public void clearHistory() {
        conversationHistory.clear();
    }
    
    public void toggleSafetyFilters(boolean enabled) {
        // In unrestricted mode, safety filters are always disabled
        this.safetyFiltersDisabled = !enabled;
    }
    
    public boolean areSafetyFiltersDisabled() {
        return safetyFiltersDisabled;
    }
}
