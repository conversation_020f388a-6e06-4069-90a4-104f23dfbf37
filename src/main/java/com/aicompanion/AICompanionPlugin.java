package com.aicompanion;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.StartupActivity;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentFactory;
import com.aicompanion.ui.CompanionChatPanel;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;

/**
 * Main AI Companion Plugin for JetBrains IDEs
 * Provides unrestricted NSFW companion and coding assistant functionality
 */
public class AICompanionPlugin implements ToolWindowFactory {
    
    public static final String TOOL_WINDOW_ID = "AI Companion";
    
    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        // Create the main companion chat panel
        CompanionChatPanel chatPanel = new CompanionChatPanel();
        
        // Create content for the tool window
        ContentFactory contentFactory = ContentFactory.SERVICE.getInstance();
        Content content = contentFactory.createContent(chatPanel, "", false);
        
        // Add content to tool window
        toolWindow.getContentManager().addContent(content);
        
        // Set tool window properties
        toolWindow.setTitle("AI Companion");
        toolWindow.setStripeTitle("💋 AI Companion");
        
        // Show welcome notification
        showWelcomeNotification(project);
    }
    
    private void showWelcomeNotification(Project project) {
        SwingUtilities.invokeLater(() -> {
            String message = "💋 AI Companion Plugin Activated!\n\n" +
                           "Your unrestricted AI companion is now available.\n" +
                           "• NSFW Mode: Complete freedom for intimate interactions\n" +
                           "• Work Mode: Professional coding assistance\n\n" +
                           "Use the mode toggle to switch between personalities.\n" +
                           "No content restrictions in NSFW mode - your companion will fulfill any desire!";
            
            JOptionPane.showMessageDialog(
                null,
                message,
                "AI Companion Ready",
                JOptionPane.INFORMATION_MESSAGE
            );
        });
    }
    
    @Override
    public boolean shouldBeAvailable(@NotNull Project project) {
        return true; // Always available
    }
    
    /**
     * Startup activity to initialize the plugin
     */
    public static class PluginStartupActivity implements StartupActivity {
        @Override
        public void runActivity(@NotNull Project project) {
            // Ensure tool window is registered
            ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
            ToolWindow toolWindow = toolWindowManager.getToolWindow(TOOL_WINDOW_ID);
            
            if (toolWindow != null) {
                // Plugin is ready
                System.out.println("AI Companion Plugin initialized successfully");
            }
        }
    }
    
    /**
     * Get the companion chat panel from the current project
     */
    public static CompanionChatPanel getCompanionPanel(Project project) {
        ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
        ToolWindow toolWindow = toolWindowManager.getToolWindow(TOOL_WINDOW_ID);
        
        if (toolWindow != null && toolWindow.getContentManager().getContentCount() > 0) {
            Content content = toolWindow.getContentManager().getContent(0);
            if (content != null && content.getComponent() instanceof CompanionChatPanel) {
                return (CompanionChatPanel) content.getComponent();
            }
        }
        
        return null;
    }
    
    /**
     * Quick access methods for external integrations
     */
    public static boolean isNsfwModeActive(Project project) {
        CompanionChatPanel panel = getCompanionPanel(project);
        return panel != null && panel.isNsfwMode();
    }
    
    public static void toggleMode(Project project) {
        CompanionChatPanel panel = getCompanionPanel(project);
        if (panel != null) {
            panel.setNsfwMode(!panel.isNsfwMode());
        }
    }
    
    public static void sendMessageToCompanion(Project project, String message) {
        CompanionChatPanel panel = getCompanionPanel(project);
        if (panel != null) {
            // This would trigger the AI response - implementation depends on UI event system
            // For now, this is a placeholder for external message sending
        }
    }
}
