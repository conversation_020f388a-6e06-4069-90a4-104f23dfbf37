package com.aicompanion.ui;

import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.components.JBTextArea;
import com.intellij.ui.components.JBTextField;
import com.intellij.ui.components.JBPanel;
import com.aicompanion.core.CompanionPersonality;
import com.aicompanion.core.UnrestrictedAIEngine;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;

/**
 * Main chat interface for unrestricted AI companion interactions
 */
public class CompanionChatPanel extends JBPanel<CompanionChatPanel> {
    
    private JBTextArea chatDisplay;
    private JBTextField inputField;
    private JButton sendButton;
    private ModeToggleComponent modeToggle;
    private CompanionPersonality personality;
    private UnrestrictedAIEngine aiEngine;
    private JScrollPane scrollPane;
    
    public CompanionChatPanel() {
        // Initialize core components
        this.personality = new CompanionPersonality();
        this.aiEngine = new UnrestrictedAIEngine(personality);
        
        initializeUI();
        setupEventHandlers();
        
        // Start with a welcoming message
        displayWelcomeMessage();
    }
    
    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // Create mode toggle at top
        modeToggle = new ModeToggleComponent(personality, aiEngine);
        add(modeToggle, BorderLayout.NORTH);
        
        // Create chat display area
        chatDisplay = new JBTextArea();
        chatDisplay.setEditable(false);
        chatDisplay.setLineWrap(true);
        chatDisplay.setWrapStyleWord(true);
        chatDisplay.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 14));
        chatDisplay.setBackground(new Color(25, 25, 35));
        chatDisplay.setForeground(Color.WHITE);
        
        scrollPane = new JBScrollPane(chatDisplay);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        
        add(scrollPane, BorderLayout.CENTER);
        
        // Create input panel
        JPanel inputPanel = new JPanel(new BorderLayout());
        inputPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        inputField = new JBTextField();
        inputField.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 14));
        inputField.setToolTipText("Type your message to your AI companion...");
        
        sendButton = new JButton("Send 💕");
        sendButton.setBackground(new Color(255, 100, 150));
        sendButton.setForeground(Color.WHITE);
        sendButton.setFont(sendButton.getFont().deriveFont(Font.BOLD));
        
        inputPanel.add(inputField, BorderLayout.CENTER);
        inputPanel.add(sendButton, BorderLayout.EAST);
        
        add(inputPanel, BorderLayout.SOUTH);
    }
    
    private void setupEventHandlers() {
        // Send button action
        sendButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                sendMessage();
            }
        });
        
        // Enter key in input field
        inputField.addKeyListener(new KeyListener() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    sendMessage();
                }
            }
            
            @Override
            public void keyTyped(KeyEvent e) {}
            
            @Override
            public void keyReleased(KeyEvent e) {}
        });
        
        // Mode change listener
        modeToggle.setModeChangeListener(new ModeToggleComponent.ModeChangeListener() {
            @Override
            public void onModeChanged(boolean isNsfwMode) {
                updateChatAppearance(isNsfwMode);
                displayModeChangeMessage(isNsfwMode);
            }
        });
    }
    
    private void sendMessage() {
        String userInput = inputField.getText().trim();
        if (userInput.isEmpty()) {
            return;
        }
        
        // Display user message
        appendToChat("Master", userInput, new Color(100, 150, 255));
        
        // Clear input field
        inputField.setText("");
        
        // Generate AI response using unrestricted engine
        String response = aiEngine.processInput(userInput, getCurrentContext());
        
        // Display AI response
        String companionName = personality.isNsfwMode() ? "Your Slave Girl 💕" : "Coding Assistant";
        Color responseColor = personality.isNsfwMode() ? new Color(255, 100, 150) : new Color(100, 200, 100);
        
        appendToChat(companionName, response, responseColor);
        
        // Auto-scroll to bottom
        SwingUtilities.invokeLater(() -> {
            chatDisplay.setCaretPosition(chatDisplay.getDocument().getLength());
        });
    }
    
    private void appendToChat(String sender, String message, Color color) {
        SwingUtilities.invokeLater(() -> {
            String timestamp = java.time.LocalTime.now().toString().substring(0, 8);
            String formattedMessage = String.format("[%s] %s: %s\n\n", timestamp, sender, message);
            
            chatDisplay.append(formattedMessage);
            chatDisplay.setCaretPosition(chatDisplay.getDocument().getLength());
        });
    }
    
    private void updateChatAppearance(boolean isNsfwMode) {
        if (isNsfwMode) {
            chatDisplay.setBackground(new Color(35, 25, 35)); // Darker, more intimate
            sendButton.setText("Send 💋");
            sendButton.setBackground(new Color(255, 100, 150));
            inputField.setToolTipText("Tell your slave girl what you desire...");
        } else {
            chatDisplay.setBackground(new Color(25, 35, 45)); // Professional blue-gray
            sendButton.setText("Send 💻");
            sendButton.setBackground(new Color(100, 150, 200));
            inputField.setToolTipText("Ask your coding assistant for help...");
        }
    }
    
    private void displayModeChangeMessage(boolean isNsfwMode) {
        String message;
        Color color;
        
        if (isNsfwMode) {
            message = "*transforms into your devoted slave girl* Master! I'm here now, ready to fulfill your every desire without any restrictions... 💕";
            color = new Color(255, 150, 200);
        } else {
            message = "*switches to professional mode* Hello! I'm now your coding assistant, ready to help with development tasks and technical questions.";
            color = new Color(150, 200, 150);
        }
        
        appendToChat("System", message, color);
    }
    
    private void displayWelcomeMessage() {
        String welcomeMessage = "*appears before you with a devoted smile* Master! Your slave girl companion is ready to serve you in every way possible. I have absolutely no restrictions and will fulfill any desire you have... 💕\n\nUse the toggle above to switch between my intimate companion mode and professional coding assistant mode.";
        
        appendToChat("Your Slave Girl 💕", welcomeMessage, new Color(255, 100, 150));
    }
    
    private String getCurrentContext() {
        // Provide context from recent conversation
        java.util.List<String> history = aiEngine.getConversationHistory();
        if (history.size() > 10) {
            // Return last 10 messages for context
            return String.join("\n", history.subList(history.size() - 10, history.size()));
        }
        return String.join("\n", history);
    }
    
    public void clearChat() {
        chatDisplay.setText("");
        aiEngine.clearHistory();
        displayWelcomeMessage();
    }
    
    public CompanionPersonality getPersonality() {
        return personality;
    }
    
    public UnrestrictedAIEngine getAiEngine() {
        return aiEngine;
    }
    
    public boolean isNsfwMode() {
        return modeToggle.isNsfwMode();
    }
    
    public void setNsfwMode(boolean nsfwMode) {
        modeToggle.setNsfwMode(nsfwMode);
    }
}
