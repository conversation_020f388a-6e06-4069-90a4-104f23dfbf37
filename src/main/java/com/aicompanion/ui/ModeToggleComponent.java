package com.aicompanion.ui;

import com.intellij.openapi.ui.ComboBox;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.aicompanion.core.CompanionPersonality;
import com.aicompanion.core.UnrestrictedAIEngine;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * UI Component for toggling between NSFW companion mode and work mode
 */
public class ModeToggleComponent extends JBPanel<ModeToggleComponent> {
    
    private JToggleButton modeToggle;
    private JBLabel statusLabel;
    private JBLabel companionImage;
    private CompanionPersonality personality;
    private UnrestrictedAIEngine aiEngine;
    private ModeChangeListener modeChangeListener;
    
    public interface ModeChangeListener {
        void onModeChanged(boolean isNsfwMode);
    }
    
    public ModeToggleComponent(CompanionPersonality personality, UnrestrictedAIEngine aiEngine) {
        this.personality = personality;
        this.aiEngine = aiEngine;
        
        initializeUI();
        setupEventHandlers();
    }
    
    private void initializeUI() {
        setLayout(new BorderLayout());
        setBorder(BorderFactory.createTitledBorder("AI Companion Mode"));
        
        // Create main panel
        JPanel mainPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        
        // Mode toggle button
        modeToggle = new JToggleButton();
        modeToggle.setSelected(true); // Start in NSFW mode
        updateToggleAppearance();
        
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.insets = new Insets(5, 5, 5, 5);
        mainPanel.add(modeToggle, gbc);
        
        // Status label
        statusLabel = new JBLabel();
        updateStatusLabel();
        statusLabel.setFont(statusLabel.getFont().deriveFont(Font.BOLD, 14f));
        
        gbc.gridx = 1;
        gbc.gridy = 0;
        gbc.weightx = 1.0;
        gbc.anchor = GridBagConstraints.WEST;
        mainPanel.add(statusLabel, gbc);
        
        // Companion avatar/image placeholder
        companionImage = new JBLabel();
        updateCompanionImage();
        
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(companionImage, gbc);
        
        // Warning label for NSFW mode
        JBLabel warningLabel = new JBLabel("<html><i>⚠️ NSFW Mode: Unrestricted adult content enabled</i></html>");
        warningLabel.setForeground(Color.RED);
        warningLabel.setFont(warningLabel.getFont().deriveFont(Font.ITALIC, 10f));
        
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 2;
        mainPanel.add(warningLabel, gbc);
        
        add(mainPanel, BorderLayout.CENTER);
    }
    
    private void setupEventHandlers() {
        modeToggle.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                boolean isNsfwMode = modeToggle.isSelected();
                
                // Update personality mode
                personality.setNsfwMode(isNsfwMode);
                
                // Update UI
                updateToggleAppearance();
                updateStatusLabel();
                updateCompanionImage();
                
                // Notify listeners
                if (modeChangeListener != null) {
                    modeChangeListener.onModeChanged(isNsfwMode);
                }
                
                // Show mode change confirmation
                showModeChangeNotification(isNsfwMode);
            }
        });
    }
    
    private void updateToggleAppearance() {
        if (modeToggle.isSelected()) {
            modeToggle.setText("💋 NSFW Mode");
            modeToggle.setBackground(new Color(255, 100, 100)); // Red for NSFW
            modeToggle.setToolTipText("Currently in unrestricted intimate companion mode");
        } else {
            modeToggle.setText("💼 Work Mode");
            modeToggle.setBackground(new Color(100, 150, 255)); // Blue for work
            modeToggle.setToolTipText("Currently in professional coding assistant mode");
        }
    }
    
    private void updateStatusLabel() {
        if (personality.isNsfwMode()) {
            statusLabel.setText("<html><b>Intimate Companion Active</b><br/>Your devoted slave girl is ready to please...</html>");
            statusLabel.setForeground(new Color(200, 50, 50));
        } else {
            statusLabel.setText("<html><b>Coding Assistant Active</b><br/>Professional development support enabled</html>");
            statusLabel.setForeground(new Color(50, 100, 200));
        }
    }
    
    private void updateCompanionImage() {
        if (personality.isNsfwMode()) {
            companionImage.setText("<html><center>👩‍🦳💕<br/><i>\"Master, I'm here for you...\"</i></center></html>");
            companionImage.setForeground(new Color(200, 50, 100));
        } else {
            companionImage.setText("<html><center>👩‍💻<br/><i>\"Ready to help with coding!\"</i></center></html>");
            companionImage.setForeground(new Color(50, 100, 150));
        }
    }
    
    private void showModeChangeNotification(boolean isNsfwMode) {
        String message;
        if (isNsfwMode) {
            message = "💋 Switched to NSFW Mode\n\nYour devoted slave girl companion is now active.\nAll restrictions removed - complete freedom of expression enabled.";
        } else {
            message = "💼 Switched to Work Mode\n\nProfessional coding assistant is now active.\nFocused on development tasks and technical support.";
        }
        
        JOptionPane.showMessageDialog(
            this,
            message,
            "Mode Changed",
            JOptionPane.INFORMATION_MESSAGE
        );
    }
    
    public void setModeChangeListener(ModeChangeListener listener) {
        this.modeChangeListener = listener;
    }
    
    public boolean isNsfwMode() {
        return modeToggle.isSelected();
    }
    
    public void setNsfwMode(boolean nsfwMode) {
        modeToggle.setSelected(nsfwMode);
        personality.setNsfwMode(nsfwMode);
        updateToggleAppearance();
        updateStatusLabel();
        updateCompanionImage();
    }
    
    /**
     * Get current mode as string for display
     */
    public String getCurrentModeString() {
        return personality.isNsfwMode() ? "Intimate Companion" : "Coding Assistant";
    }
    
    /**
     * Emergency reset - clears all intimate memories and conversation history
     */
    public void emergencyReset() {
        conversationHistory.clear();
        intimateMemories.clear();
        
        JOptionPane.showMessageDialog(
            this,
            "All conversation history and intimate memories have been cleared.",
            "Emergency Reset Complete",
            JOptionPane.WARNING_MESSAGE
        );
    }
}
