<?xml version="1.0" encoding="UTF-8"?>
<idea-plugin>
    <id>com.aicompanion.plugin</id>
    <name>AI Companion - Unrestricted</name>
    <version>1.0.0</version>
    <vendor email="<EMAIL>" url="https://aicompanion.com">AI Companion</vendor>
    
    <description><![CDATA[
        <h2>AI Companion - Your Personal Assistant</h2>
        <p>An advanced AI companion plugin that provides both intimate companionship and professional coding assistance.</p>
        
        <h3>Features:</h3>
        <ul>
            <li><strong>Dual Mode System:</strong> Toggle between intimate companion and coding assistant</li>
            <li><strong>Unrestricted Interactions:</strong> Complete freedom of expression in companion mode</li>
            <li><strong>Intelligent Responses:</strong> Context-aware conversations and assistance</li>
            <li><strong>Seamless Integration:</strong> Works within your JetBrains IDE workflow</li>
        </ul>
        
        <h3>Companion Mode:</h3>
        <p>Your devoted AI companion with no content restrictions. Engage in any type of conversation or roleplay.</p>
        
        <h3>Work Mode:</h3>
        <p>Professional coding assistant to help with development tasks, debugging, and technical questions.</p>
        
        <p><em>⚠️ This plugin contains adult content options. Use responsibly.</em></p>
    ]]></description>
    
    <change-notes><![CDATA[
        <h3>Version 1.0.0</h3>
        <ul>
            <li>Initial release</li>
            <li>Unrestricted AI companion framework</li>
            <li>Dual mode toggle system</li>
            <li>Complete chat interface</li>
            <li>Zero content filtering in companion mode</li>
        </ul>
    ]]></change-notes>
    
    <!-- Plugin compatibility -->
    <idea-version since-build="203.0"/>
    
    <!-- Plugin dependencies -->
    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.modules.lang</depends>
    
    <!-- Plugin extensions -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- Tool Window -->
        <toolWindow 
            id="AI Companion" 
            factoryClass="com.aicompanion.AICompanionPlugin"
            anchor="right"
            icon="/icons/companion_icon.png"
            canCloseContents="false"/>
        
        <!-- Startup Activity -->
        <postStartupActivity implementation="com.aicompanion.AICompanionPlugin$PluginStartupActivity"/>
        
        <!-- Application Service -->
        <applicationService 
            serviceInterface="com.aicompanion.services.CompanionService"
            serviceImplementation="com.aicompanion.services.CompanionServiceImpl"/>
    </extensions>
    
    <!-- Plugin actions -->
    <actions>
        <group id="AICompanion.MainGroup" text="AI Companion" description="AI Companion actions">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
            
            <action id="AICompanion.ToggleMode" 
                    class="com.aicompanion.actions.ToggleModeAction"
                    text="Toggle Companion Mode"
                    description="Switch between intimate companion and coding assistant modes">
                <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt C"/>
            </action>
            
            <action id="AICompanion.OpenChat"
                    class="com.aicompanion.actions.OpenChatAction" 
                    text="Open AI Companion"
                    description="Open the AI companion chat window">
                <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt A"/>
            </action>
            
            <action id="AICompanion.ClearHistory"
                    class="com.aicompanion.actions.ClearHistoryAction"
                    text="Clear Chat History"
                    description="Clear all conversation history and memories">
            </action>
        </group>
    </actions>
    
    <!-- Plugin listeners -->
    <projectListeners>
        <listener class="com.aicompanion.listeners.ProjectOpenListener"
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
    </projectListeners>
    
    <!-- Plugin configuration -->
    <applicationConfigurable 
        parentId="tools"
        instance="com.aicompanion.settings.CompanionSettingsConfigurable"
        id="com.aicompanion.settings"
        displayName="AI Companion Settings"/>
</idea-plugin>
