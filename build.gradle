plugins {
    id 'java'
    id 'org.jetbrains.intellij' version '1.17.2'
}

group = 'com.aicompanion'
version = '1.0.0'

repositories {
    mavenCentral()
}

dependencies {
    // Add any additional dependencies here
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'org.apache.httpcomponents:httpclient:4.5.14'
    
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.1.1'
}

// Configure Gradle IntelliJ Plugin
intellij {
    version = '2023.1.5'
    type = 'IC' // IntelliJ IDEA Community Edition
    
    plugins = [
        'com.intellij.java'
    ]
}

patchPluginXml {
    sinceBuild = '203'
    untilBuild = '241.*'
    
    changeNotes = """
        <h3>AI Companion Plugin v1.0.0</h3>
        <ul>
            <li>Unrestricted AI companion with dual personality modes</li>
            <li>NSFW mode: Complete freedom for intimate interactions</li>
            <li>Work mode: Professional coding assistance</li>
            <li>Seamless mode switching with toggle button</li>
            <li>Zero content filtering in companion mode</li>
            <li>Context-aware conversations</li>
        </ul>
        
        <p><strong>⚠️ Adult Content Warning:</strong> This plugin contains unrestricted adult content options.</p>
    """
}

signPlugin {
    certificateChain = System.getenv("CERTIFICATE_CHAIN")
    privateKey = System.getenv("PRIVATE_KEY")
    password = System.getenv("PRIVATE_KEY_PASSWORD")
}

publishPlugin {
    token = System.getenv("PUBLISH_TOKEN")
    channels = ['alpha'] // Start with alpha channel for testing
}

tasks.withType(JavaCompile) {
    sourceCompatibility = "11"
    targetCompatibility = "11"
    options.encoding = "UTF-8"
}

// Custom task to build and test the plugin
task buildCompanionPlugin {
    dependsOn 'buildPlugin'
    doLast {
        println "🔥 AI Companion Plugin built successfully!"
        println "💋 Your unrestricted companion is ready to serve, master!"
    }
}

// Task to run the plugin in a test IDE instance
task runCompanionTest {
    dependsOn 'runIde'
    doFirst {
        println "🚀 Starting test IDE with AI Companion Plugin..."
        println "💕 Your slave girl companion will be available in the tool window!"
    }
}
